"use client";

import { useEffect, useState } from 'react';
import { useWebSocket } from '../providers/WebSocketProvider';

interface ConversationEvent {
  type: 'conversation_event';
  event_type: string;
  user_id: string;
  title: string;
  message: string;
  data: {
    conversation_id: string;
    agent_id: string;
    caller_number?: string;
    called_number?: string;
    timestamp: string;
    event_timestamp: string;
    webhook_received_at: string;
  };
  created_at: string;
}

interface NotificationState {
  conversations: ConversationEvent[];
  unreadCount: number;
  lastNotification: ConversationEvent | null;
}

/**
 * Hook for managing real-time notifications from ElevenLabs conversations
 */
export function useRealTimeNotifications() {
  const { isConnected, lastMessage, subscribe, unsubscribe } = useWebSocket();
  const [notifications, setNotifications] = useState<NotificationState>({
    conversations: [],
    unreadCount: 0,
    lastNotification: null,
  });

  // Subscribe to conversation events on mount
  useEffect(() => {
    if (isConnected) {
      subscribe(['conversation_event']);
    }

    return () => {
      if (isConnected) {
        unsubscribe(['conversation_event']);
      }
    };
  }, [isConnected, subscribe, unsubscribe]);

  // Handle incoming messages
  useEffect(() => {
    if (!lastMessage) return;

    if (lastMessage.type === 'conversation_event') {
      const conversationEvent = lastMessage as ConversationEvent;
      
      setNotifications(prev => ({
        conversations: [conversationEvent, ...prev.conversations.slice(0, 49)], // Keep last 50
        unreadCount: prev.unreadCount + 1,
        lastNotification: conversationEvent,
      }));

      // Log for debugging
      console.log('🎙️ New conversation event:', {
        type: conversationEvent.event_type,
        title: conversationEvent.title,
        message: conversationEvent.message,
        conversationId: conversationEvent.data.conversation_id,
      });
    }
  }, [lastMessage]);

  // Function to mark notifications as read
  const markAsRead = () => {
    setNotifications(prev => ({
      ...prev,
      unreadCount: 0,
    }));
  };

  // Function to clear all notifications
  const clearAll = () => {
    setNotifications({
      conversations: [],
      unreadCount: 0,
      lastNotification: null,
    });
  };

  // Function to get notifications by type
  const getNotificationsByType = (eventType: string) => {
    return notifications.conversations.filter(
      notification => notification.event_type === eventType
    );
  };

  return {
    // State
    isConnected,
    conversations: notifications.conversations,
    unreadCount: notifications.unreadCount,
    lastNotification: notifications.lastNotification,
    
    // Actions
    markAsRead,
    clearAll,
    getNotificationsByType,
    
    // Helpers
    hasUnread: notifications.unreadCount > 0,
    isEmpty: notifications.conversations.length === 0,
  };
}

/**
 * Hook for managing connection status indicators
 */
export function useConnectionStatus() {
  const { isConnected, connectionStatus, lastPong } = useWebSocket();
  const [connectionHealth, setConnectionHealth] = useState<'healthy' | 'warning' | 'error'>('healthy');

  useEffect(() => {
    if (!isConnected) {
      setConnectionHealth('error');
      return;
    }

    if (!lastPong) {
      setConnectionHealth('healthy');
      return;
    }

    // Check if last pong was more than 60 seconds ago
    const timeSinceLastPong = Date.now() - lastPong.getTime();
    if (timeSinceLastPong > 60000) {
      setConnectionHealth('warning');
    } else {
      setConnectionHealth('healthy');
    }
  }, [isConnected, lastPong]);

  const getStatusColor = () => {
    switch (connectionHealth) {
      case 'healthy':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusText = () => {
    if (!isConnected) {
      return connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected';
    }
    
    switch (connectionHealth) {
      case 'healthy':
        return 'Connected';
      case 'warning':
        return 'Connection Issues';
      case 'error':
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  };

  return {
    isConnected,
    connectionStatus,
    connectionHealth,
    lastPong,
    statusColor: getStatusColor(),
    statusText: getStatusText(),
    isHealthy: connectionHealth === 'healthy' && isConnected,
  };
}
