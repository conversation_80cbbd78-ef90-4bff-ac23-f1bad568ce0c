/**
 * WebSocket Integration Test for Phase 5D
 * Tests WebSocket server functionality and ElevenLabs integration
 */

const WebSocket = require('ws');
const axios = require('axios');

// Configuration
const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3006';
const WS_URL = process.env.WS_URL || 'ws://localhost:3006/ws';
const TEST_USER_ID = 'test-user-123';

// Test utilities
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

class WebSocketTester {
  constructor() {
    this.ws = null;
    this.messages = [];
    this.connected = false;
  }

  async connect(userId = TEST_USER_ID) {
    return new Promise((resolve, reject) => {
      console.log(`🔌 Connecting to WebSocket: ${WS_URL}?userId=${userId}`);
      
      this.ws = new WebSocket(`${WS_URL}?userId=${userId}`);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        this.connected = true;
        resolve();
      });
      
      this.ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', message);
        this.messages.push(message);
      });
      
      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        reject(error);
      });
      
      this.ws.on('close', (code, reason) => {
        console.log(`🔌 WebSocket disconnected - Code: ${code}, Reason: ${reason}`);
        this.connected = false;
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        if (!this.connected) {
          reject(new Error('WebSocket connection timeout'));
        }
      }, 5000);
    });
  }

  send(message) {
    if (this.connected && this.ws) {
      this.ws.send(JSON.stringify(message));
      console.log('📤 Sent message:', message);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }

  getMessages() {
    return this.messages;
  }

  clearMessages() {
    this.messages = [];
  }
}

// Test functions
async function testWebSocketConnection() {
  console.log('\n🧪 Test 1: WebSocket Connection');
  
  const tester = new WebSocketTester();
  
  try {
    await tester.connect();
    
    // Wait for connection established message
    await delay(1000);
    
    const messages = tester.getMessages();
    const connectionMessage = messages.find(m => m.type === 'connection_established');
    
    if (connectionMessage) {
      console.log('✅ Connection established message received');
    } else {
      console.log('❌ Connection established message not received');
    }
    
    tester.disconnect();
    return true;
  } catch (error) {
    console.error('❌ WebSocket connection test failed:', error);
    return false;
  }
}

async function testPingPong() {
  console.log('\n🧪 Test 2: Ping/Pong Heartbeat');
  
  const tester = new WebSocketTester();
  
  try {
    await tester.connect();
    await delay(500);
    
    tester.clearMessages();
    tester.send({ type: 'ping' });
    
    await delay(1000);
    
    const messages = tester.getMessages();
    const pongMessage = messages.find(m => m.type === 'pong');
    
    if (pongMessage) {
      console.log('✅ Pong response received');
    } else {
      console.log('❌ Pong response not received');
    }
    
    tester.disconnect();
    return !!pongMessage;
  } catch (error) {
    console.error('❌ Ping/Pong test failed:', error);
    return false;
  }
}

async function testEventSubscription() {
  console.log('\n🧪 Test 3: Event Subscription');
  
  const tester = new WebSocketTester();
  
  try {
    await tester.connect();
    await delay(500);
    
    tester.clearMessages();
    tester.send({ 
      type: 'subscribe', 
      events: ['conversation_event', 'call_status_updated'] 
    });
    
    await delay(1000);
    
    const messages = tester.getMessages();
    const subscriptionMessage = messages.find(m => m.type === 'subscription_confirmed');
    
    if (subscriptionMessage) {
      console.log('✅ Subscription confirmed:', subscriptionMessage.events);
    } else {
      console.log('❌ Subscription confirmation not received');
    }
    
    tester.disconnect();
    return !!subscriptionMessage;
  } catch (error) {
    console.error('❌ Event subscription test failed:', error);
    return false;
  }
}

async function testWebSocketStatus() {
  console.log('\n🧪 Test 4: WebSocket Status Endpoint');
  
  try {
    const response = await axios.get(`${BACKEND_URL}/api/websocket/status`);
    
    if (response.data.success) {
      console.log('✅ WebSocket status endpoint working');
      console.log('📊 Stats:', response.data.data);
      return true;
    } else {
      console.log('❌ WebSocket status endpoint failed');
      return false;
    }
  } catch (error) {
    console.error('❌ WebSocket status test failed:', error);
    return false;
  }
}

async function testElevenLabsWebhookIntegration() {
  console.log('\n🧪 Test 5: ElevenLabs Webhook Integration');
  
  const tester = new WebSocketTester();
  
  try {
    await tester.connect();
    await delay(500);
    
    // Subscribe to conversation events
    tester.send({ 
      type: 'subscribe', 
      events: ['conversation_event'] 
    });
    
    await delay(500);
    tester.clearMessages();
    
    // Simulate ElevenLabs webhook with proper structure
    const webhookPayload = {
      event_type: 'conversation.started',
      event_timestamp: new Date().toISOString(),
      data: {
        conversation_id: 'test-conv-123',
        agent_id: 'test-agent-456',
        caller_number: '+1234567890',
        called_number: '+0987654321',
        timestamp: new Date().toISOString()
      }
    };
    
    console.log('📤 Sending test webhook...');

    // Clear existing messages and set up tracking for conversation event
    tester.clearMessages();
    let conversationEvent = null;

    // Wait a moment to ensure WebSocket connection is stable
    await new Promise(resolve => setTimeout(resolve, 500));

    // Send webhook
    console.log('📤 Sending test webhook...');
    console.log(`🔗 WebSocket connection status: ${tester.connected ? 'CONNECTED' : 'DISCONNECTED'}`);

    const response = await axios.post(`${BACKEND_URL}/api/webhooks/elevenlabs-test`, webhookPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ Webhook response: ${response.status}`);
    console.log(`🔗 WebSocket connection status after webhook: ${tester.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
    console.log('⏳ Waiting for WebSocket message...');

    // Wait for the conversation event with timeout using Promise
    const waitForMessage = new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 200; // 20 seconds total (200 * 100ms)

      const checkForMessage = () => {
        // Check the tester's messages array for conversation_event
        const messages = tester.getMessages();
        const foundEvent = messages.find(m => m.type === 'conversation_event');

        if (foundEvent) {
          conversationEvent = foundEvent;
          console.log('🎉 Conversation event found in messages!');
          console.log('📨 Event details:', foundEvent);
          // Wait an additional 500ms after receiving the message to ensure full processing
          setTimeout(() => resolve(true), 500);
          return;
        }

        attempts++;
        if (attempts >= maxAttempts) {
          console.log('⏳ Timeout reached, checking one more time...');
          resolve(false);
          return;
        }

        if (attempts % 20 === 0) {
          console.log(`⏳ Still waiting... (${attempts * 100}ms elapsed)`);
          console.log(`📊 Messages so far: ${messages.length}`);
          console.log(`🔗 Connection status: ${tester.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
        }

        setTimeout(checkForMessage, 100);
      };

      // Start checking after a small delay to allow webhook processing
      setTimeout(checkForMessage, 500);
    });

    const messageReceived = await waitForMessage;

    // Keep connection open longer to ensure webhook processing completes
    if (!messageReceived) {
      console.log('⏳ Giving webhook processing more time...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Check one more time if we received the message during the extra wait
      const messages = tester.getMessages();
      const foundEvent = messages.find(m => m.type === 'conversation_event');
      if (foundEvent) {
        conversationEvent = foundEvent;
        console.log('🎉 Message received during extended wait!');
      }
    }

    // Wait a bit more before disconnecting to ensure all processing is complete
    await new Promise(resolve => setTimeout(resolve, 500));

    // Disconnect after ensuring all processing is complete
    tester.disconnect();

    // Check if we received the conversation event (either during wait or extended wait)
    const finalResult = messageReceived || conversationEvent;

    if (finalResult && conversationEvent) {
      console.log('✅ Conversation event received via WebSocket');
      console.log('📨 Event data:', conversationEvent);
      return true;
    } else {
      console.log('❌ Conversation event not received via WebSocket');
      console.log('📨 All messages received:', tester.getMessages());
      console.log('📨 Conversation event variable:', conversationEvent);
      return false;
    }
  } catch (error) {
    console.error('❌ ElevenLabs webhook integration test failed:', error);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting WebSocket Integration Tests for Phase 5D');
  console.log(`🔗 Backend URL: ${BACKEND_URL}`);
  console.log(`🔌 WebSocket URL: ${WS_URL}`);
  
  const results = [];
  
  // Run tests
  results.push(await testWebSocketConnection());
  results.push(await testPingPong());
  results.push(await testEventSubscription());
  results.push(await testWebSocketStatus());
  results.push(await testElevenLabsWebhookIntegration());
  
  // Summary
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! WebSocket integration is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  WebSocketTester,
  runTests
};
