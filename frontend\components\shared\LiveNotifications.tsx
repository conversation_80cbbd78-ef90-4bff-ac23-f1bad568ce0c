"use client";

import React, { useEffect, useState } from 'react';
import { useRealTimeNotifications } from '../../hooks/useRealTimeNotifications';
import { BellIcon, PhoneIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';

interface LiveNotificationsProps {
  maxVisible?: number;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

/**
 * Live Notifications Component
 * Displays real-time notifications with Apple-like design and animations
 */
export function LiveNotifications({ 
  maxVisible = 5,
  autoHide = true,
  autoHideDelay = 5000,
  className = ''
}: LiveNotificationsProps) {
  const { 
    conversations, 
    unreadCount, 
    lastNotification, 
    markAsRead, 
    clearAll,
    hasUnread,
    isEmpty 
  } = useRealTimeNotifications();

  const [visibleNotifications, setVisibleNotifications] = useState<string[]>([]);
  const [dismissedNotifications, setDismissedNotifications] = useState<Set<string>>(new Set());

  // Show new notifications
  useEffect(() => {
    if (lastNotification && !dismissedNotifications.has(lastNotification.data.conversation_id)) {
      const notificationId = lastNotification.data.conversation_id;
      
      setVisibleNotifications(prev => {
        if (!prev.includes(notificationId)) {
          return [notificationId, ...prev.slice(0, maxVisible - 1)];
        }
        return prev;
      });

      // Auto-hide notification
      if (autoHide) {
        setTimeout(() => {
          setVisibleNotifications(prev => prev.filter(id => id !== notificationId));
        }, autoHideDelay);
      }
    }
  }, [lastNotification, maxVisible, autoHide, autoHideDelay, dismissedNotifications]);

  // Dismiss notification
  const dismissNotification = (conversationId: string) => {
    setVisibleNotifications(prev => prev.filter(id => id !== conversationId));
    setDismissedNotifications(prev => new Set([...prev, conversationId]));
  };

  // Get notification by conversation ID
  const getNotificationById = (conversationId: string) => {
    return conversations.find(conv => conv.data.conversation_id === conversationId);
  };

  // Get icon for event type
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'conversation_started':
        return <PhoneIcon className="w-5 h-5 text-green-500" />;
      case 'conversation_ended':
        return <PhoneIcon className="w-5 h-5 text-red-500" />;
      default:
        return <BellSolidIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  // Format phone number
  const formatPhoneNumber = (phoneNumber?: string) => {
    if (!phoneNumber) return 'Unknown';
    return phoneNumber.replace(/^\+1/, '').replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isEmpty || visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 space-y-2 ${className}`}>
      {visibleNotifications.map(conversationId => {
        const notification = getNotificationById(conversationId);
        if (!notification) return null;

        return (
          <div
            key={conversationId}
            className="
              bg-white dark:bg-gray-800 
              rounded-lg shadow-lg border border-gray-200 dark:border-gray-700
              p-4 min-w-80 max-w-96
              transform transition-all duration-300 ease-in-out
              animate-slide-in-right
            "
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                {/* Event Icon */}
                <div className="flex-shrink-0 mt-0.5">
                  {getEventIcon(notification.event_type)}
                </div>

                {/* Notification Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                      {notification.title}
                    </h4>
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                      {formatTimestamp(notification.created_at)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                    {notification.message}
                  </p>

                  {/* Call Details */}
                  {(notification.data.caller_number || notification.data.called_number) && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                      {notification.data.caller_number && (
                        <div>From: {formatPhoneNumber(notification.data.caller_number)}</div>
                      )}
                      {notification.data.called_number && (
                        <div>To: {formatPhoneNumber(notification.data.called_number)}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Dismiss Button */}
              <button
                onClick={() => dismissNotification(conversationId)}
                className="
                  flex-shrink-0 ml-2 p-1 rounded-full
                  text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
                  hover:bg-gray-100 dark:hover:bg-gray-700
                  transition-colors duration-200
                "
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
}

/**
 * Notification Bell Icon with Badge
 * Shows unread count and provides access to notifications
 */
export function NotificationBell({ onClick }: { onClick?: () => void }) {
  const { unreadCount, hasUnread, markAsRead } = useRealTimeNotifications();

  const handleClick = () => {
    if (hasUnread) {
      markAsRead();
    }
    onClick?.();
  };

  return (
    <button
      onClick={handleClick}
      className="
        relative p-2 rounded-full
        text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white
        hover:bg-gray-100 dark:hover:bg-gray-700
        transition-colors duration-200
      "
    >
      {hasUnread ? (
        <BellSolidIcon className="w-6 h-6" />
      ) : (
        <BellIcon className="w-6 h-6" />
      )}
      
      {/* Unread Badge */}
      {hasUnread && (
        <span className="
          absolute -top-1 -right-1
          bg-red-500 text-white text-xs font-bold
          rounded-full w-5 h-5 flex items-center justify-center
          animate-pulse
        ">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
    </button>
  );
}
