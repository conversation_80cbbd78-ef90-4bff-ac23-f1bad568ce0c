# 🔌 WebSocket Integration Guide

This guide explains how to use the real-time WebSocket functionality in CallSaver.app.

## 🚀 Quick Start

### 1. Basic WebSocket Usage

```tsx
import { useWebSocket } from '../providers/WebSocketProvider';

function MyComponent() {
  const { isConnected, send, lastMessage } = useWebSocket();
  
  const sendMessage = () => {
    send({ type: 'test', message: 'Hello WebSocket!' });
  };
  
  return (
    <div>
      <p>Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={sendMessage} disabled={!isConnected}>
        Send Message
      </button>
    </div>
  );
}
```

### 2. Real-time Notifications

```tsx
import { useRealTimeNotifications } from '../hooks/useRealTimeNotifications';

function NotificationComponent() {
  const { 
    conversations, 
    unreadCount, 
    hasUnread, 
    markAsRead 
  } = useRealTimeNotifications();
  
  return (
    <div>
      {hasUnread && (
        <div className="notification-badge">
          {unreadCount} new notifications
        </div>
      )}
      <button onClick={markAsRead}>Mark as Read</button>
    </div>
  );
}
```

### 3. Connection Status

```tsx
import { ConnectionStatusIndicator } from '../components/shared/ConnectionStatusIndicator';

function Header() {
  return (
    <div className="header">
      <h1>CallSaver Dashboard</h1>
      <ConnectionStatusIndicator showText size="md" />
    </div>
  );
}
```

## 🎨 UI Components

### ConnectionStatusIndicator
- **Props**: `showText`, `size`, `className`
- **Sizes**: `sm`, `md`, `lg`
- **Features**: Real-time status, tooltips, animations

### LiveNotifications
- **Props**: `maxVisible`, `autoHide`, `autoHideDelay`, `className`
- **Features**: Toast notifications, auto-dismiss, slide animations

### NotificationBell
- **Props**: `onClick`
- **Features**: Unread badge, click handling, icon states

## 🔧 Hooks Reference

### useWebSocket()
```tsx
const {
  isConnected,        // boolean - connection status
  connectionStatus,   // string - detailed status
  lastMessage,        // object - last received message
  send,              // function - send message
  subscribe,         // function - subscribe to events
  unsubscribe        // function - unsubscribe from events
} = useWebSocket();
```

### useRealTimeNotifications()
```tsx
const {
  conversations,      // array - notification history
  unreadCount,       // number - unread notifications
  lastNotification,  // object - most recent notification
  hasUnread,         // boolean - has unread notifications
  isEmpty,           // boolean - no notifications
  markAsRead,        // function - mark all as read
  clearAll,          // function - clear all notifications
  getNotificationsByType // function - filter by type
} = useRealTimeNotifications();
```

### useConnectionStatus()
```tsx
const {
  isConnected,       // boolean - connection status
  connectionStatus,  // string - connection state
  connectionHealth,  // string - health assessment
  statusColor,       // string - CSS color class
  statusText,        // string - display text
  isHealthy         // boolean - connection is healthy
} = useConnectionStatus();
```

## 📡 Message Types

### Conversation Events
```typescript
interface ConversationEvent {
  type: 'conversation_event';
  event_type: 'conversation_started' | 'conversation_ended';
  user_id: string;
  title: string;
  message: string;
  data: {
    conversation_id: string;
    agent_id: string;
    caller_number?: string;
    called_number?: string;
    timestamp: string;
  };
}
```

### Subscription Messages
```typescript
// Subscribe to events
send({ type: 'subscribe', events: ['conversation_event'] });

// Unsubscribe from events
send({ type: 'unsubscribe', events: ['conversation_event'] });
```

## 🎯 Best Practices

### 1. Connection Management
- Always check `isConnected` before sending messages
- Handle connection failures gracefully
- Use connection status indicators for user feedback

### 2. Event Subscription
- Subscribe to specific events only when needed
- Unsubscribe when component unmounts
- Use the provided hooks for automatic management

### 3. Performance
- Limit notification history (default: 50 items)
- Use proper React keys for notification lists
- Implement virtualization for large notification lists

### 4. Error Handling
```tsx
const { isConnected, connectionStatus } = useWebSocket();

if (connectionStatus === 'error') {
  return <div>Connection error. Please refresh.</div>;
}

if (!isConnected) {
  return <div>Connecting...</div>;
}
```

## 🧪 Testing

### Test WebSocket Connection
Visit `/websocket-test` to access the test dashboard with:
- Connection status monitoring
- Message sending/receiving
- Event subscription testing
- Webhook simulation

### Manual Testing
```tsx
// Send test message
send({ type: 'test', message: 'Hello' });

// Subscribe to events
subscribe(['conversation_event']);

// Trigger test webhook
fetch('/api/webhooks/elevenlabs/test', {
  method: 'POST',
  body: JSON.stringify({ event_type: 'conversation_started' })
});
```

## 🔧 Configuration

### Environment Variables
```bash
NEXT_PUBLIC_WS_URL=ws://localhost:3006/ws
```

### Provider Setup
The WebSocket provider is already integrated in `AppProviders.tsx`:

```tsx
<QueryProvider>
  <WebSocketProvider>
    {children}
  </WebSocketProvider>
</QueryProvider>
```

## 🎨 Styling

### Tailwind Classes
- `animate-slide-in-right` - Notification entrance animation
- Connection status colors: `text-green-500`, `text-yellow-500`, `text-red-500`
- Notification badges: `bg-red-500 text-white rounded-full`

### Custom Animations
```css
@keyframes slide-in-right {
  0% { transform: translateX(100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **WebSocket not connecting**
   - Check backend server is running on port 3006
   - Verify `NEXT_PUBLIC_WS_URL` environment variable

2. **Notifications not appearing**
   - Ensure subscription to correct event types
   - Check browser console for WebSocket errors

3. **Connection status not updating**
   - Verify WebSocket provider is properly wrapped around components
   - Check for multiple provider instances

### Debug Mode
Enable debug logging by checking browser console for WebSocket messages prefixed with emojis:
- 🔌 Connection events
- 📝 Subscription events  
- 📨 Message events
- ⚠️ Warning messages

## 📚 Additional Resources

- [WebSocket Test Dashboard](/websocket-test)
- [Backend WebSocket Documentation](../backend/docs/WEBSOCKET_SERVICE.md)
- [ElevenLabs Integration Guide](../backend/docs/ELEVENLABS_INTEGRATION.md)
