"use client";

import React, { useState } from 'react';
import { ConnectionStatusIndicator, ConnectionStatusCard } from '../../components/shared/ConnectionStatusIndicator';
import { LiveNotifications, NotificationBell } from '../../components/shared/LiveNotifications';
import { useRealTimeNotifications, useConnectionStatus } from '../../hooks/useRealTimeNotifications';
import { useWebSocket } from '../../providers/WebSocketProvider';

/**
 * WebSocket Test Page
 * Demonstrates real-time WebSocket functionality with ElevenLabs integration
 */
export default function WebSocketTestPage() {
  const { 
    isConnected, 
    connectionStatus, 
    lastMessage, 
    send, 
    subscribe, 
    unsubscribe 
  } = useWebSocket();
  
  const { 
    conversations, 
    unreadCount, 
    lastNotification, 
    markAsRead, 
    clearAll,
    hasUnread 
  } = useRealTimeNotifications();

  const [testMessage, setTestMessage] = useState('');
  const [subscriptionEvents, setSubscriptionEvents] = useState('conversation_event,call_status_updated');

  // Send test message
  const sendTestMessage = () => {
    if (testMessage.trim()) {
      send({
        type: 'test',
        message: testMessage,
        timestamp: new Date().toISOString()
      });
      setTestMessage('');
    }
  };

  // Send ping
  const sendPing = () => {
    send({ type: 'ping' });
  };

  // Subscribe to events
  const handleSubscribe = () => {
    const events = subscriptionEvents.split(',').map(e => e.trim()).filter(Boolean);
    subscribe(events);
  };

  // Unsubscribe from events
  const handleUnsubscribe = () => {
    const events = subscriptionEvents.split(',').map(e => e.trim()).filter(Boolean);
    unsubscribe(events);
  };

  // Trigger test webhook
  const triggerTestWebhook = async () => {
    try {
      const response = await fetch('http://localhost:3006/api/webhooks/elevenlabs/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_type: 'conversation.started',
          data: {
            conversation_id: `test-conv-${Date.now()}`,
            agent_id: 'test-agent-456',
            caller_number: '+1234567890',
            called_number: '+0987654321',
            timestamp: new Date().toISOString()
          }
        })
      });

      if (response.ok) {
        console.log('✅ Test webhook triggered successfully');
      } else {
        console.error('❌ Failed to trigger test webhook:', response.status);
      }
    } catch (error) {
      console.error('❌ Error triggering test webhook:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      {/* Live Notifications */}
      <LiveNotifications />

      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              WebSocket Test Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Test real-time WebSocket functionality and ElevenLabs integration
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <NotificationBell />
            <ConnectionStatusIndicator showText size="lg" />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Connection Status */}
          <div className="space-y-4">
            <ConnectionStatusCard />
            
            {/* WebSocket Controls */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                WebSocket Controls
              </h3>
              
              <div className="space-y-4">
                {/* Send Test Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Send Test Message
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={testMessage}
                      onChange={(e) => setTestMessage(e.target.value)}
                      placeholder="Enter test message..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      onKeyPress={(e) => e.key === 'Enter' && sendTestMessage()}
                    />
                    <button
                      onClick={sendTestMessage}
                      disabled={!isConnected || !testMessage.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Send
                    </button>
                  </div>
                </div>

                {/* Ping Button */}
                <button
                  onClick={sendPing}
                  disabled={!isConnected}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Send Ping
                </button>

                {/* Event Subscription */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Event Subscription
                  </label>
                  <input
                    type="text"
                    value={subscriptionEvents}
                    onChange={(e) => setSubscriptionEvents(e.target.value)}
                    placeholder="conversation_event,call_status_updated"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white mb-2"
                  />
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSubscribe}
                      disabled={!isConnected}
                      className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Subscribe
                    </button>
                    <button
                      onClick={handleUnsubscribe}
                      disabled={!isConnected}
                      className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Unsubscribe
                    </button>
                  </div>
                </div>

                {/* Test Webhook */}
                <button
                  onClick={triggerTestWebhook}
                  className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
                >
                  Trigger Test Webhook
                </button>
              </div>
            </div>
          </div>

          {/* Real-time Data */}
          <div className="space-y-4">
            {/* Notifications Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Notifications
                </h3>
                <div className="flex items-center space-x-2">
                  {hasUnread && (
                    <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      {unreadCount}
                    </span>
                  )}
                  <button
                    onClick={markAsRead}
                    disabled={!hasUnread}
                    className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
                  >
                    Mark Read
                  </button>
                  <button
                    onClick={clearAll}
                    disabled={conversations.length === 0}
                    className="text-sm text-red-600 hover:text-red-800 disabled:opacity-50"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {conversations.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    No notifications yet. Trigger a test webhook to see real-time updates.
                  </p>
                ) : (
                  conversations.slice(0, 10).map((notification, index) => (
                    <div
                      key={`${notification.data.conversation_id}-${index}`}
                      className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {notification.title}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(notification.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {notification.message}
                      </p>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        ID: {notification.data.conversation_id}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Last Message */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Last WebSocket Message
              </h3>
              
              {lastMessage ? (
                <pre className="text-sm bg-gray-50 dark:bg-gray-700 p-3 rounded-md overflow-x-auto">
                  {JSON.stringify(lastMessage, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  No messages received yet.
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
