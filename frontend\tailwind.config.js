/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      'xs': '475px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        background: '#0f172a',
        'gray-750': '#2d3748',
        'gray-850': '#1a202c',
      },
      backgroundColor: {
        'gradient-dark': 'linear-gradient(180deg, #1a202c 0%, #0f172a 100%)',
      },
      boxShadow: {
        'neon-blue': '0 0 15px rgba(59, 130, 246, 0.5)',
        'neon-purple': '0 0 15px rgba(139, 92, 246, 0.5)',
        'neon-green': '0 0 15px rgba(74, 222, 128, 0.5)',
        'neon-indigo': '0 0 15px rgba(99, 102, 241, 0.5)',
        'neon-yellow': '0 0 15px rgba(250, 204, 21, 0.5)',
        'neon-pink': '0 0 15px rgba(236, 72, 153, 0.5)',
        'neon-button': '0 0 20px rgba(139, 92, 246, 0.5), 0 0 40px rgba(139, 92, 246, 0.2)',
        'neon-card': '0 0 15px rgba(139, 92, 246, 0.3), inset 0 0 10px rgba(139, 92, 246, 0.1)',
        'neon-text': '0 0 10px rgba(139, 92, 246, 0.7), 0 0 20px rgba(139, 92, 246, 0.4)',
        'neon-border': '0 0 5px rgba(139, 92, 246, 0.5), inset 0 0 5px rgba(139, 92, 246, 0.2)',
      },
      animation: {
        'pulse-slow': 'pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'gradient-shift': 'gradient-shift 8s ease infinite',
        'fadeIn': 'fadeIn 0.5s ease-out',
        'slideIn': 'slideIn 0.3s ease-out',
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 3s infinite alternate',
        'neon-pulse': 'neon-pulse 2s ease-in-out infinite',
        'float-particle': 'float-particle 15s linear infinite',
        'shimmer-neon': 'shimmer-neon 3s ease-in-out infinite',
        'border-glow': 'border-glow 2s ease-in-out infinite',
        'slide-in-right': 'slide-in-right 0.3s ease-out',
      },
      keyframes: {
        'gradient-shift': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        'fadeIn': {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        'slideIn': {
          '0%': { transform: 'translateY(-10px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'pulse-glow': {
          '0%, 100%': { opacity: 0.4 },
          '50%': { opacity: 0.8 },
        },
        'neon-pulse': {
          '0%, 100%': { filter: 'drop-shadow(0 0 2px rgba(139, 92, 246, 0.5))' },
          '50%': { filter: 'drop-shadow(0 0 5px rgba(139, 92, 246, 0.8))' },
        },
        'float-particle': {
          '0%, 100%': { transform: 'translate3d(0, 0, 0)', opacity: 0.3 },
          '25%': { transform: 'translate3d(50px, -30px, 0)', opacity: 0.4 },
          '50%': { transform: 'translate3d(100px, 50px, 0)', opacity: 0.5 },
          '75%': { transform: 'translate3d(50px, 100px, 0)', opacity: 0.4 },
        },
        'shimmer-neon': {
          '0%': { transform: 'translateX(-100%)', opacity: 0 },
          '50%': { transform: 'translateX(0%)', opacity: 0.5 },
          '100%': { transform: 'translateX(100%)', opacity: 0 },
        },
        'border-glow': {
          '0%, 100%': { borderColor: 'rgba(139, 92, 246, 0.3)' },
          '50%': { borderColor: 'rgba(139, 92, 246, 0.7)' },
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(100%)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
      },
      backgroundImage: {
        'grid-pattern': 'linear-gradient(to right, #1e293b 1px, transparent 1px), linear-gradient(to bottom, #1e293b 1px, transparent 1px)',
        'neon-gradient': 'linear-gradient(90deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05) 50%, rgba(139, 92, 246, 0.1))',
        'neon-radial': 'radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.2) 0%, transparent 70%)',
      },
      backgroundSize: {
        'grid': '20px 20px',
      },
      backdropBlur: {
        'xs': '2px',
      },
      textShadow: {
        'neon': '0 0 10px rgba(139, 92, 246, 0.7), 0 0 20px rgba(139, 92, 246, 0.4)',
      },
    },
  },
  plugins: [],
};