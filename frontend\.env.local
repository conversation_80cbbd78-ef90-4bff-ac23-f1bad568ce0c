# Point frontend API calls to the local backend server
# Prefix with NEXT_PUBLIC_ to expose to the browser
NEXT_PUBLIC_API_BASE_URL=http://localhost:3006/api

# WebSocket Configuration
NEXT_PUBLIC_WS_URL=ws://localhost:3006/ws

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Supabase Configuration - CallSaver Modernization Project
NEXT_PUBLIC_SUPABASE_URL=https://zzkytozgnociyjvhthfk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU
