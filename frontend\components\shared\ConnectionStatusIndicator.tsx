"use client";

import React from 'react';
import { useConnectionStatus } from '../../hooks/useRealTimeNotifications';

interface ConnectionStatusIndicatorProps {
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Connection Status Indicator Component
 * Shows the real-time WebSocket connection status with Apple-like design
 */
export function ConnectionStatusIndicator({ 
  showText = false, 
  size = 'md',
  className = '' 
}: ConnectionStatusIndicatorProps) {
  const { 
    isConnected, 
    connectionStatus, 
    connectionHealth, 
    statusColor, 
    statusText,
    lastPong 
  } = useConnectionStatus();

  // Size configurations
  const sizeConfig = {
    sm: {
      dot: 'w-2 h-2',
      text: 'text-xs',
      container: 'gap-1.5',
    },
    md: {
      dot: 'w-3 h-3',
      text: 'text-sm',
      container: 'gap-2',
    },
    lg: {
      dot: 'w-4 h-4',
      text: 'text-base',
      container: 'gap-2.5',
    },
  };

  const config = sizeConfig[size];

  // Animation classes based on connection status
  const getAnimationClass = () => {
    if (connectionStatus === 'connecting') {
      return 'animate-pulse';
    }
    if (isConnected && connectionHealth === 'healthy') {
      return 'animate-pulse'; // Subtle pulse for healthy connection
    }
    return '';
  };

  // Background color based on status
  const getBackgroundColor = () => {
    if (!isConnected) {
      return 'bg-red-500';
    }
    
    switch (connectionHealth) {
      case 'healthy':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Tooltip content
  const getTooltipContent = () => {
    const parts = [
      `Status: ${statusText}`,
      `Connection: ${connectionStatus}`,
    ];
    
    if (lastPong) {
      const timeSinceLastPong = Math.floor((Date.now() - lastPong.getTime()) / 1000);
      parts.push(`Last ping: ${timeSinceLastPong}s ago`);
    }
    
    return parts.join('\n');
  };

  return (
    <div 
      className={`flex items-center ${config.container} ${className}`}
      title={getTooltipContent()}
    >
      {/* Status Dot */}
      <div className="relative">
        <div
          className={`
            ${config.dot} 
            rounded-full 
            ${getBackgroundColor()} 
            ${getAnimationClass()}
            transition-all duration-300 ease-in-out
          `}
        />
        
        {/* Outer ring for healthy connection */}
        {isConnected && connectionHealth === 'healthy' && (
          <div
            className={`
              absolute inset-0 
              ${config.dot} 
              rounded-full 
              bg-green-500 
              opacity-20 
              animate-ping
            `}
          />
        )}
      </div>

      {/* Status Text */}
      {showText && (
        <span className={`${config.text} font-medium ${statusColor} transition-colors duration-300`}>
          {statusText}
        </span>
      )}
    </div>
  );
}

/**
 * Detailed Connection Status Card
 * Shows comprehensive connection information
 */
export function ConnectionStatusCard() {
  const { 
    isConnected, 
    connectionStatus, 
    connectionHealth, 
    lastPong,
    statusText 
  } = useConnectionStatus();

  const formatLastPong = () => {
    if (!lastPong) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - lastPong.getTime();
    
    if (diff < 60000) {
      return `${Math.floor(diff / 1000)}s ago`;
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}m ago`;
    } else {
      return lastPong.toLocaleTimeString();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
          Connection Status
        </h3>
        <ConnectionStatusIndicator size="sm" />
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Status:</span>
          <span className={`font-medium ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
            {statusText}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Connection:</span>
          <span className="font-medium text-gray-900 dark:text-white capitalize">
            {connectionStatus}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Health:</span>
          <span className={`font-medium capitalize ${
            connectionHealth === 'healthy' ? 'text-green-600' : 
            connectionHealth === 'warning' ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {connectionHealth}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Last Ping:</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {formatLastPong()}
          </span>
        </div>
      </div>
    </div>
  );
}
