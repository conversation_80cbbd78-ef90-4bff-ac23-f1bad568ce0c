"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useUIStore } from '../stores/uiStore';
import { queryClient } from './QueryProvider';

// Define the WebSocket context type
interface WebSocketContextType {
  isConnected: boolean;
  lastPong: Date | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  send: (data: any) => void;
  subscribe: (events: string[]) => void;
  unsubscribe: (events: string[]) => void;
  lastMessage: any;
}

// Create the context with default values
const WebSocketContext = createContext<WebSocketContextType>({
  isConnected: false,
  lastPong: null,
  connectionStatus: 'disconnected',
  send: () => {}, // Empty function as placeholder
  subscribe: () => {},
  unsubscribe: () => {},
  lastMessage: null,
});

interface WebSocketProviderProps {
  children: React.ReactNode;
}

/**
 * WebSocket Provider
 * Manages WebSocket connection and message handling
 */
export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastPong, setLastPong] = useState<Date | null>(null);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [subscribedEvents, setSubscribedEvents] = useState<Set<string>>(new Set());
  
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);
  const incrementUnreadNotifications = useUIStore(state => state.incrementUnreadNotificationsCount);
  
  // Connection and reconnection logic
  useEffect(() => {
    // Only connect if user is authenticated
    if (!isAuthenticated || !user) {
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
        setConnectionStatus('disconnected');
      }
      return;
    }

    // WebSocket server URL from environment
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;

    console.log('🔌 Connecting to WebSocket:', `${wsUrl}?userId=${user.id}`);
    setConnectionStatus('connecting');

    // Initialize WebSocket
    const ws = new WebSocket(`${wsUrl}?userId=${user.id}`);
    
    // Set up WebSocket event handlers
    ws.onopen = () => {
      console.log('✅ WebSocket connected');
      setIsConnected(true);
      setConnectionStatus('connected');
      setReconnectAttempt(0); // Reset reconnect attempts

      // Subscribe to default events for ElevenLabs integration
      const defaultEvents = ['conversation_event', 'call_status_updated', 'notification'];
      setSubscribedEvents(new Set(defaultEvents));

      ws.send(JSON.stringify({
        type: 'subscribe',
        events: defaultEvents
      }));
    };
    
    ws.onclose = (event) => {
      console.log('🔌 WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);
      setConnectionStatus('disconnected');

      // Implement reconnection logic with exponential backoff
      if (isAuthenticated) {
        const backoffTime = Math.min(1000 * Math.pow(2, reconnectAttempt), 30000); // Max 30 seconds
        console.log(`🔄 Reconnecting in ${backoffTime}ms (attempt ${reconnectAttempt + 1})`);

        setTimeout(() => {
          setReconnectAttempt(prev => prev + 1);
        }, backoffTime);
      }
    };

    ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      setConnectionStatus('error');
    };
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('📨 WebSocket message received:', message);
        setLastMessage(message);
        handleMessage(message);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    // Store the WebSocket instance
    setSocket(ws);
    
    // Clean up on unmount
    return () => {
      ws.close();
    };
  }, [isAuthenticated, user, reconnectAttempt]);
  
  // Handle different types of WebSocket messages
  const handleMessage = (message: any) => {
    // Handle different message types
    switch (message.type) {
      case 'connection_established':
        console.log('🎉 Connection established:', message.message);
        break;

      case 'subscription_confirmed':
        console.log('✅ Subscription confirmed for events:', message.events);
        break;

      case 'pong':
        setLastPong(new Date());
        console.log('🏓 Pong received');
        break;

      case 'conversation_event':
        console.log('🎙️ Conversation event:', message.event_type, message.message);
        // Show notification for conversation events
        if (message.title && message.message) {
          // You can integrate with a toast notification system here
          console.log(`📢 ${message.title}: ${message.message}`);
        }
        // Invalidate call logs to refresh the list
        queryClient.invalidateQueries({ queryKey: ['call-logs'] });
        break;

      case 'notification':
        // When a new notification arrives
        incrementUnreadNotifications();
        // Invalidate notifications query to refetch
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
        break;

      case 'call_status_updated':
        // Invalidate call logs to refresh the list
        queryClient.invalidateQueries({ queryKey: ['call-logs'] });
        break;

      case 'credit_updated':
        // Update credit balance in UI
        queryClient.invalidateQueries({ queryKey: ['credits'] });
        break;

      case 'new_number_assigned':
        // Refresh phone numbers list
        queryClient.invalidateQueries({ queryKey: ['numbers'] });
        break;

      default:
        console.log('❓ Unhandled WebSocket message type:', message.type);
    }
  };
  
  // Function to send a message through the WebSocket
  const send = (data: any) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify(data));
    } else {
      console.warn('⚠️ Cannot send message: WebSocket is not connected');
    }
  };

  // Function to subscribe to events
  const subscribe = (events: string[]) => {
    if (socket && isConnected) {
      const newEvents = events.filter(event => !subscribedEvents.has(event));
      if (newEvents.length > 0) {
        setSubscribedEvents(prev => new Set([...prev, ...newEvents]));
        send({
          type: 'subscribe',
          events: newEvents
        });
        console.log('📝 Subscribed to events:', newEvents);
      }
    } else {
      console.warn('⚠️ Cannot subscribe: WebSocket is not connected');
    }
  };

  // Function to unsubscribe from events
  const unsubscribe = (events: string[]) => {
    if (socket && isConnected) {
      const eventsToRemove = events.filter(event => subscribedEvents.has(event));
      if (eventsToRemove.length > 0) {
        setSubscribedEvents(prev => {
          const newSet = new Set(prev);
          eventsToRemove.forEach(event => newSet.delete(event));
          return newSet;
        });
        send({
          type: 'unsubscribe',
          events: eventsToRemove
        });
        console.log('📝 Unsubscribed from events:', eventsToRemove);
      }
    } else {
      console.warn('⚠️ Cannot unsubscribe: WebSocket is not connected');
    }
  };
  
  // Keep WebSocket alive with ping/pong (if server supports it)
  useEffect(() => {
    if (!isConnected) return;
    
    const pingInterval = setInterval(() => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        send({ type: 'ping' });
      }
    }, 30000); // Send ping every 30 seconds
    
    return () => {
      clearInterval(pingInterval);
    };
  }, [socket, isConnected]);
  
  // Context value
  const value = {
    isConnected,
    connectionStatus,
    lastPong,
    lastMessage,
    send,
    subscribe,
    unsubscribe,
  };
  
  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
}

// Custom hook to use the WebSocket context
export const useWebSocket = () => useContext(WebSocketContext);
