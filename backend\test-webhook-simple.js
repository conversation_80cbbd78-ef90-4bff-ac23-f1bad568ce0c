const WebSocket = require('ws');
const axios = require('axios');

async function testWebhookIntegration() {
  const BACKEND_URL = 'http://localhost:3006';
  const WS_URL = 'ws://localhost:3006/ws?userId=test-user-123';
  
  console.log('🧪 Simple Webhook Integration Test');
  console.log('🔗 Connecting to:', WS_URL);
  
  const ws = new WebSocket(WS_URL);
  let connected = false;
  let messages = [];
  
  ws.on('open', () => {
    console.log('✅ WebSocket connected');
    connected = true;
  });
  
  ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('📨 Received:', message);
    messages.push(message);
  });
  
  ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed - Code: ${code}, Reason: ${reason}`);
    connected = false;
  });
  
  ws.on('error', (error) => {
    console.log('❌ WebSocket error:', error);
  });
  
  // Wait for connection
  await new Promise(resolve => {
    const checkConnection = () => {
      if (connected) {
        resolve();
      } else {
        setTimeout(checkConnection, 100);
      }
    };
    checkConnection();
  });
  
  // Subscribe to events
  console.log('📤 Subscribing to conversation_event');
  ws.send(JSON.stringify({ type: 'subscribe', events: ['conversation_event'] }));
  
  // Wait for subscription confirmation
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Send webhook
  console.log('📤 Sending webhook...');
  console.log(`🔗 Connection status before webhook: ${connected ? 'CONNECTED' : 'DISCONNECTED'}`);
  
  const webhookPayload = {
    event_type: 'conversation.started',
    data: {
      conversation_id: 'test-conv-123',
      agent_id: 'test-agent-456',
      caller_number: '+1234567890',
      called_number: '+0987654321',
      timestamp: new Date().toISOString()
    }
  };
  
  try {
    const response = await axios.post(`${BACKEND_URL}/api/webhooks/elevenlabs-test`, webhookPayload, {
      headers: { 'Content-Type': 'application/json' }
    });
    console.log(`✅ Webhook response: ${response.status}`);
  } catch (error) {
    console.log('❌ Webhook error:', error.message);
  }
  
  console.log(`🔗 Connection status after webhook: ${connected ? 'CONNECTED' : 'DISCONNECTED'}`);
  
  // Wait for webhook processing and message
  console.log('⏳ Waiting for webhook message...');
  let attempts = 0;
  const maxAttempts = 100; // 10 seconds
  
  while (attempts < maxAttempts) {
    const conversationEvent = messages.find(m => m.type === 'conversation_event');
    if (conversationEvent) {
      console.log('🎉 Conversation event received!');
      console.log('📨 Event:', conversationEvent);
      break;
    }
    
    if (attempts % 10 === 0) {
      console.log(`⏳ Still waiting... (${attempts * 100}ms elapsed)`);
      console.log(`🔗 Connection status: ${connected ? 'CONNECTED' : 'DISCONNECTED'}`);
      console.log(`📊 Messages so far: ${messages.length}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }
  
  if (attempts >= maxAttempts) {
    console.log('❌ Timeout reached');
  }
  
  console.log('📨 All messages received:', messages);
  
  // Close connection
  ws.close();
  
  console.log('🏁 Test completed');
}

testWebhookIntegration().catch(console.error);
